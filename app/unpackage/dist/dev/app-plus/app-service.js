if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((a=>t.resolve(e()).then((()=>a))),(a=>t.resolve(e()).then((()=>{throw a}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.<PERSON>Uint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...a){uni.__log__&&uni.__log__(e,t,...a)}const a=t=>(a,n=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,a,n)},n=a("onShow"),o=a("onHide"),s=a("onLaunch");function r(e,t,a){return Array.isArray(e)?(e.length=Math.max(e.length,t),e.splice(t,1,a),a):(e[t]=a,a)}function i(e,t){Array.isArray(e)?e.splice(t,1):delete e[t]}function c(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}const l="function"==typeof Proxy;let d,u,m;function p(){return void 0!==d||("undefined"!=typeof window&&window.performance?(d=!0,u=window.performance):"undefined"!=typeof global&&(null===(e=global.perf_hooks)||void 0===e?void 0:e.performance)?(d=!0,u=global.perf_hooks.performance):d=!1),d?u.now():Date.now();var e}class v{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const a={};if(e.settings)for(const r in e.settings){const t=e.settings[r];a[r]=t.defaultValue}const n=`__vue-devtools-plugin-settings__${e.id}`;let o=Object.assign({},a);try{const e=localStorage.getItem(n),t=JSON.parse(e);Object.assign(o,t)}catch(s){}this.fallbacks={getSettings:()=>o,setSettings(e){try{localStorage.setItem(n,JSON.stringify(e))}catch(s){}o=e},now:()=>p()},t&&t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((a=>{this.targetQueue.push({method:t,args:e,resolve:a})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function g(e,t){const a=e,n=c(),o=c().__VUE_DEVTOOLS_GLOBAL_HOOK__,s=l&&a.enableEarlyProxy;if(!o||!n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&s){const e=s?new v(a,o):null;(n.__VUE_DEVTOOLS_PLUGINS__=n.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:a,setupFn:t,proxy:e}),e&&t(e.proxiedTarget)}else o.emit("devtools-plugin:setup",e,t)}
/*!
   * pinia v2.1.7
   * (c) 2023 Eduardo San Martin Morote
   * @license MIT
   */const f=e=>m=e,y=Symbol("pinia");function h(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var w,b;(b=w||(w={})).direct="direct",b.patchObject="patch object",b.patchFunction="patch function";const E="undefined"!=typeof window,V=E,N=(()=>"object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof global&&global.global===global?global:"object"==typeof globalThis?globalThis:{HTMLElement:null})();function k(e,t,a){const n=new XMLHttpRequest;n.open("GET",e),n.responseType="blob",n.onload=function(){I(n.response,t,a)},n.onerror=function(){},n.send()}function _(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(a){}return t.status>=200&&t.status<=299}function S(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(t){const a=document.createEvent("MouseEvents");a.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(a)}}const x="object"==typeof navigator?navigator:{userAgent:""},C=(()=>/Macintosh/.test(x.userAgent)&&/AppleWebKit/.test(x.userAgent)&&!/Safari/.test(x.userAgent))(),I=E?"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype&&!C?function(e,t="download",a){const n=document.createElement("a");n.download=t,n.rel="noopener","string"==typeof e?(n.href=e,n.origin!==location.origin?_(n.href)?k(e,t,a):(n.target="_blank",S(n)):S(n)):(n.href=URL.createObjectURL(e),setTimeout((function(){URL.revokeObjectURL(n.href)}),4e4),setTimeout((function(){S(n)}),0))}:"msSaveOrOpenBlob"in x?function(e,t="download",a){if("string"==typeof e)if(_(e))k(e,t,a);else{const t=document.createElement("a");t.href=e,t.target="_blank",setTimeout((function(){S(t)}))}else navigator.msSaveOrOpenBlob(function(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}(e,a),t)}:function(e,t,a,n){(n=n||open("","_blank"))&&(n.document.title=n.document.body.innerText="downloading...");if("string"==typeof e)return k(e,t,a);const o="application/octet-stream"===e.type,s=/constructor/i.test(String(N.HTMLElement))||"safari"in N,r=/CriOS\/[\d]+/.test(navigator.userAgent);if((r||o&&s||C)&&"undefined"!=typeof FileReader){const t=new FileReader;t.onloadend=function(){let e=t.result;if("string"!=typeof e)throw n=null,new Error("Wrong reader.result type");e=r?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),n?n.location.href=e:location.assign(e),n=null},t.readAsDataURL(e)}else{const t=URL.createObjectURL(e);n?n.location.assign(t):location.href=t,n=null,setTimeout((function(){URL.revokeObjectURL(t)}),4e4)}}:()=>{};function T(e,t){"function"==typeof __VUE_DEVTOOLS_TOAST__&&__VUE_DEVTOOLS_TOAST__("🍍 "+e,t)}function P(e){return"_a"in e&&"install"in e}function A(){if(!("clipboard"in navigator))return T("Your browser doesn't support the Clipboard API","error"),!0}function $(e){return!!(e instanceof Error&&e.message.toLowerCase().includes("document is not focused"))&&(T('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0)}let D;async function B(e){try{const t=(D||(D=document.createElement("input"),D.type="file",D.accept=".json"),function(){return new Promise(((e,t)=>{D.onchange=async()=>{const t=D.files;if(!t)return e(null);const a=t.item(0);return e(a?{text:await a.text(),file:a}:null)},D.oncancel=()=>e(null),D.onerror=t,D.click()}))}),a=await t();if(!a)return;const{text:n,file:o}=a;O(e,JSON.parse(n)),T(`Global state imported from "${o.name}".`)}catch(t){T("Failed to import the state from JSON. Check the console for more details.","error")}}function O(e,t){for(const a in t){const n=e.state.value[a];n?Object.assign(n,t[a]):e.state.value[a]=t[a]}}function M(e){return{_custom:{display:e}}}const j="🍍 Pinia (root)",R="_root";function U(e){return P(e)?{id:R,label:j}:{id:e.$id,label:e.$id}}function L(e){return e?Array.isArray(e)?e.reduce(((e,t)=>(e.keys.push(t.key),e.operations.push(t.type),e.oldValue[t.key]=t.oldValue,e.newValue[t.key]=t.newValue,e)),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:M(e.type),key:M(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function F(e){switch(e){case w.direct:return"mutation";case w.patchFunction:case w.patchObject:return"$patch";default:return"unknown"}}let H=!0;const q=[],z="pinia:mutations",G="pinia",{assign:J}=Object,W=e=>"🍍 "+e;function Q(t,a){g({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:q,app:t},(n=>{"function"!=typeof n.now&&T("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.addTimelineLayer({id:z,label:"Pinia 🍍",color:15064968}),n.addInspector({id:G,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{!async function(e){if(!A())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),T("Global state copied to clipboard.")}catch(t){if($(t))return;T("Failed to serialize the state. Check the console for more details.","error")}}(a)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await async function(e){if(!A())try{O(e,JSON.parse(await navigator.clipboard.readText())),T("Global state pasted from clipboard.")}catch(t){if($(t))return;T("Failed to deserialize the state from clipboard. Check the console for more details.","error")}}(a),n.sendInspectorTree(G),n.sendInspectorState(G)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{!async function(e){try{I(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){T("Failed to export the state as JSON. Check the console for more details.","error")}}(a)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await B(a),n.sendInspectorTree(G),n.sendInspectorState(G)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:e=>{const t=a._s.get(e);t?"function"!=typeof t.$reset?T(`Cannot reset "${e}" store because it doesn't have a "$reset" method implemented.`,"warn"):(t.$reset(),T(`Store "${e}" reset.`)):T(`Cannot reset "${e}" store because it wasn't found.`,"warn")}}]}),n.on.inspectComponent(((t,a)=>{const n=t.componentInstance&&t.componentInstance.proxy;if(n&&n._pStores){const a=t.componentInstance.proxy._pStores;Object.values(a).forEach((a=>{t.instanceData.state.push({type:W(a.$id),key:"state",editable:!0,value:a._isOptionsAPI?{_custom:{value:e.toRaw(a.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>a.$reset()}]}}:Object.keys(a.$state).reduce(((e,t)=>(e[t]=a.$state[t],e)),{})}),a._getters&&a._getters.length&&t.instanceData.state.push({type:W(a.$id),key:"getters",editable:!1,value:a._getters.reduce(((e,t)=>{try{e[t]=a[t]}catch(n){e[t]=n}return e}),{})})}))}})),n.on.getInspectorTree((e=>{if(e.app===t&&e.inspectorId===G){let t=[a];t=t.concat(Array.from(a._s.values())),e.rootNodes=(e.filter?t.filter((t=>"$id"in t?t.$id.toLowerCase().includes(e.filter.toLowerCase()):j.toLowerCase().includes(e.filter.toLowerCase()))):t).map(U)}})),n.on.getInspectorState((e=>{if(e.app===t&&e.inspectorId===G){const t=e.nodeId===R?a:a._s.get(e.nodeId);if(!t)return;t&&(e.state=function(e){if(P(e)){const t=Array.from(e._s.keys()),a=e._s;return{state:t.map((t=>({editable:!0,key:t,value:e.state.value[t]}))),getters:t.filter((e=>a.get(e)._getters)).map((e=>{const t=a.get(e);return{editable:!1,key:e,value:t._getters.reduce(((e,a)=>(e[a]=t[a],e)),{})}}))}}const t={state:Object.keys(e.$state).map((t=>({editable:!0,key:t,value:e.$state[t]})))};return e._getters&&e._getters.length&&(t.getters=e._getters.map((t=>({editable:!1,key:t,value:e[t]})))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map((t=>({editable:!0,key:t,value:e[t]})))),t}(t))}})),n.on.editInspectorState(((e,n)=>{if(e.app===t&&e.inspectorId===G){const t=e.nodeId===R?a:a._s.get(e.nodeId);if(!t)return T(`store "${e.nodeId}" not found`,"error");const{path:n}=e;P(t)?n.unshift("state"):1===n.length&&t._customProperties.has(n[0])&&!(n[0]in t.$state)||n.unshift("$state"),H=!1,e.set(t,n,e.state.value),H=!0}})),n.on.editComponentState((e=>{if(e.type.startsWith("🍍")){const t=e.type.replace(/^🍍\s*/,""),n=a._s.get(t);if(!n)return T(`store "${t}" not found`,"error");const{path:o}=e;if("state"!==o[0])return T(`Invalid path for store "${t}":\n${o}\nOnly state can be modified.`);o[0]="$state",H=!1,e.set(n,o,e.state.value),H=!0}}))}))}let Y,K=0;function X(t,a,n){const o=a.reduce(((a,n)=>(a[n]=e.toRaw(t)[n],a)),{});for(const e in o)t[e]=function(){const a=K,s=n?new Proxy(t,{get:(...e)=>(Y=a,Reflect.get(...e)),set:(...e)=>(Y=a,Reflect.set(...e))}):t;Y=a;const r=o[e].apply(s,arguments);return Y=void 0,r}}function Z({app:t,store:a,options:n}){if(a.$id.startsWith("__hot:"))return;a._isOptionsAPI=!!n.state,X(a,Object.keys(n.actions),a._isOptionsAPI);const o=a._hotUpdate;e.toRaw(a)._hotUpdate=function(e){o.apply(this,arguments),X(a,Object.keys(e._hmrPayload.actions),!!a._isOptionsAPI)},function(t,a){q.includes(W(a.$id))||q.push(W(a.$id)),g({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:q,app:t,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},(t=>{const n="function"==typeof t.now?t.now.bind(t):Date.now;a.$onAction((({after:e,onError:o,name:s,args:r})=>{const i=K++;t.addTimelineEvent({layerId:z,event:{time:n(),title:"🛫 "+s,subtitle:"start",data:{store:M(a.$id),action:M(s),args:r},groupId:i}}),e((e=>{Y=void 0,t.addTimelineEvent({layerId:z,event:{time:n(),title:"🛬 "+s,subtitle:"end",data:{store:M(a.$id),action:M(s),args:r,result:e},groupId:i}})})),o((e=>{Y=void 0,t.addTimelineEvent({layerId:z,event:{time:n(),logType:"error",title:"💥 "+s,subtitle:"end",data:{store:M(a.$id),action:M(s),args:r,error:e},groupId:i}})}))}),!0),a._customProperties.forEach((o=>{e.watch((()=>e.unref(a[o])),((e,a)=>{t.notifyComponentUpdate(),t.sendInspectorState(G),H&&t.addTimelineEvent({layerId:z,event:{time:n(),title:"Change",subtitle:o,data:{newValue:e,oldValue:a},groupId:Y}})}),{deep:!0})})),a.$subscribe((({events:e,type:o},s)=>{if(t.notifyComponentUpdate(),t.sendInspectorState(G),!H)return;const r={time:n(),title:F(o),data:J({store:M(a.$id)},L(e)),groupId:Y};o===w.patchFunction?r.subtitle="⤵️":o===w.patchObject?r.subtitle="🧩":e&&!Array.isArray(e)&&(r.subtitle=e.type),e&&(r.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:e}}),t.addTimelineEvent({layerId:z,event:r})}),{detached:!0,flush:"sync"});const o=a._hotUpdate;a._hotUpdate=e.markRaw((e=>{o(e),t.addTimelineEvent({layerId:z,event:{time:n(),title:"🔥 "+a.$id,subtitle:"HMR update",data:{store:M(a.$id),info:M("HMR update")}}}),t.notifyComponentUpdate(),t.sendInspectorTree(G),t.sendInspectorState(G)}));const{$dispose:s}=a;a.$dispose=()=>{s(),t.notifyComponentUpdate(),t.sendInspectorTree(G),t.sendInspectorState(G),t.getSettings().logStoreChanges&&T(`Disposed "${a.$id}" store 🗑`)},t.notifyComponentUpdate(),t.sendInspectorTree(G),t.sendInspectorState(G),t.getSettings().logStoreChanges&&T(`"${a.$id}" store installed 🆕`)}))}(t,a)}function ee(t,a){for(const n in a){const o=a[n];if(!(n in t))continue;const s=t[n];h(s)&&h(o)&&!e.isRef(o)&&!e.isReactive(o)?t[n]=ee(s,o):t[n]=o}return t}const te=()=>{};function ae(t,a,n,o=te){t.push(a);const s=()=>{const e=t.indexOf(a);e>-1&&(t.splice(e,1),o())};return!n&&e.getCurrentScope()&&e.onScopeDispose(s),s}function ne(e,...t){e.slice().forEach((e=>{e(...t)}))}const oe=e=>e();function se(t,a){t instanceof Map&&a instanceof Map&&a.forEach(((e,a)=>t.set(a,e))),t instanceof Set&&a instanceof Set&&a.forEach(t.add,t);for(const n in a){if(!a.hasOwnProperty(n))continue;const o=a[n],s=t[n];h(s)&&h(o)&&t.hasOwnProperty(n)&&!e.isRef(o)&&!e.isReactive(o)?t[n]=se(s,o):t[n]=o}return t}const re=Symbol("pinia:skipHydration");const{assign:ie}=Object;function ce(t){return!(!e.isRef(t)||!t.effect)}function le(t,a,n,o){const{state:s,actions:r,getters:i}=a,c=n.state.value[t];let l;return l=de(t,(function(){c||o||(n.state.value[t]=s?s():{});const a=o?e.toRefs(e.ref(s?s():{}).value):e.toRefs(n.state.value[t]);return ie(a,r,Object.keys(i||{}).reduce(((a,o)=>(a[o]=e.markRaw(e.computed((()=>{f(n);const e=n._s.get(t);return i[o].call(e,e)}))),a)),{}))}),a,n,o,!0),l}function de(t,a,n={},o,s,c){let l;const d=ie({actions:{}},n);if(!o._e.active)throw new Error("Pinia destroyed");const u={deep:!0};let m,p;u.onTrigger=e=>{m?v=e:0!=m||T._hotUpdating||Array.isArray(v)&&v.push(e)};let v,g=[],y=[];const b=o.state.value[t];c||b||s||(o.state.value[t]={});const N=e.ref({});let k;function _(a){let n;m=p=!1,v=[],"function"==typeof a?(a(o.state.value[t]),n={type:w.patchFunction,storeId:t,events:v}):(se(o.state.value[t],a),n={type:w.patchObject,payload:a,storeId:t,events:v});const s=k=Symbol();e.nextTick().then((()=>{k===s&&(m=!0)})),p=!0,ne(g,n,o.state.value[t])}const S=c?function(){const{state:e}=n,t=e?e():{};this.$patch((e=>{ie(e,t)}))}:()=>{throw new Error(`🍍: Store "${t}" is built using the setup syntax and does not implement $reset().`)};function x(e,a){return function(){f(o);const n=Array.from(arguments),s=[],r=[];function i(e){s.push(e)}function c(e){r.push(e)}let l;ne(y,{args:n,name:e,store:T,after:i,onError:c});try{l=a.apply(this&&this.$id===t?this:T,n)}catch(d){throw ne(r,d),d}return l instanceof Promise?l.then((e=>(ne(s,e),e))).catch((e=>(ne(r,e),Promise.reject(e)))):(ne(s,l),l)}}const C=e.markRaw({actions:{},getters:{},state:[],hotState:N}),I={_p:o,$id:t,$onAction:ae.bind(null,y),$patch:_,$reset:S,$subscribe(a,n={}){const s=ae(g,a,n.detached,(()=>r())),r=l.run((()=>e.watch((()=>o.state.value[t]),(e=>{("sync"===n.flush?p:m)&&a({storeId:t,type:w.direct,events:v},e)}),ie({},u,n))));return s},$dispose:function(){l.stop(),g=[],y=[],o._s.delete(t)}},T=e.reactive(ie({_hmrPayload:C,_customProperties:e.markRaw(new Set)},I));o._s.set(t,T);const P=(o._a&&o._a.runWithContext||oe)((()=>o._e.run((()=>(l=e.effectScope()).run(a)))));for(const i in P){const a=P[i];if(e.isRef(a)&&!ce(a)||e.isReactive(a))s?r(N.value,i,e.toRef(P,i)):c||(!b||h(A=a)&&A.hasOwnProperty(re)||(e.isRef(a)?a.value=b[i]:se(a,b[i])),o.state.value[t][i]=a),C.state.push(i);else if("function"==typeof a){const e=s?a:x(i,a);P[i]=e,C.actions[i]=a,d.actions[i]=a}else if(ce(a)&&(C.getters[i]=c?n.getters[i]:a,E)){(P._getters||(P._getters=e.markRaw([]))).push(i)}}var A;if(ie(T,P),ie(e.toRaw(T),P),Object.defineProperty(T,"$state",{get:()=>s?N.value:o.state.value[t],set:e=>{if(s)throw new Error("cannot set hotState");_((t=>{ie(t,e)}))}}),T._hotUpdate=e.markRaw((a=>{T._hotUpdating=!0,a._hmrPayload.state.forEach((t=>{if(t in T.$state){const e=a.$state[t],n=T.$state[t];"object"==typeof e&&h(e)&&h(n)?ee(e,n):a.$state[t]=n}r(T,t,e.toRef(a.$state,t))})),Object.keys(T.$state).forEach((e=>{e in a.$state||i(T,e)})),m=!1,p=!1,o.state.value[t]=e.toRef(a._hmrPayload,"hotState"),p=!0,e.nextTick().then((()=>{m=!0}));for(const e in a._hmrPayload.actions){const t=a[e];r(T,e,x(e,t))}for(const t in a._hmrPayload.getters){const n=a._hmrPayload.getters[t],s=c?e.computed((()=>(f(o),n.call(T,T)))):n;r(T,t,s)}Object.keys(T._hmrPayload.getters).forEach((e=>{e in a._hmrPayload.getters||i(T,e)})),Object.keys(T._hmrPayload.actions).forEach((e=>{e in a._hmrPayload.actions||i(T,e)})),T._hmrPayload=a._hmrPayload,T._getters=a._getters,T._hotUpdating=!1})),V){const e={writable:!0,configurable:!0,enumerable:!1};["_p","_hmrPayload","_getters","_customProperties"].forEach((t=>{Object.defineProperty(T,t,ie({value:T[t]},e))}))}return o._p.forEach((e=>{if(V){const t=l.run((()=>e({store:T,app:o._a,pinia:o,options:d})));Object.keys(t||{}).forEach((e=>T._customProperties.add(e))),ie(T,t)}else ie(T,l.run((()=>e({store:T,app:o._a,pinia:o,options:d}))))})),T.$state&&"object"==typeof T.$state&&"function"==typeof T.$state.constructor&&T.$state.constructor.toString().includes("[native code]"),b&&c&&n.hydrate&&n.hydrate(T.$state,b),m=!0,p=!0,T}function ue(t,a,n){let o,s;const r="function"==typeof a;if("string"==typeof t)o=t,s=r?n:a;else if(s=t,o=t.id,"string"!=typeof o)throw new Error('[🍍]: "defineStore()" must be passed a store id as its first argument.');function i(t,n){const c=e.hasInjectionContext();if((t=t||(c?e.inject(y,null):null))&&f(t),!m)throw new Error('[🍍]: "getActivePinia()" was called but there was no active Pinia. Are you trying to use a store before calling "app.use(pinia)"?\nSee https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\nThis will fail in production.');(t=m)._s.has(o)||(r?de(o,a,s,t):le(o,s,t),i._pinia=t);const l=t._s.get(o);if(n){const e="__hot:"+o,i=r?de(e,a,s,t,!0):le(e,ie({},s),t,!0);n._hotUpdate(i),delete t.state.value[e],t._s.delete(e)}if(E){const t=e.getCurrentInstance();if(t&&t.proxy&&!n){const e=t.proxy;("_pStores"in e?e._pStores:e._pStores={})[o]=l}}return l}return i.$id=o,i}const me="http://192.168.101.18:8090",pe=ue("user",(()=>{const a=e.ref(""),n=e.ref({id:null,phone:"",nickname:"",balance_count:0,balance_duration:0,free_trial_count:3,ab_test_group:""}),o=e.computed((()=>!!a.value)),s=e=>{a.value=e,uni.setStorageSync("token",e)},r=e=>{n.value={...n.value,...e},uni.setStorageSync("userInfo",n.value)},i=()=>{const e=uni.getStorageSync("token"),t=uni.getStorageSync("userInfo");e&&(a.value=e),t&&(n.value={...n.value,...t})};return i(),{token:a,userInfo:n,isLoggedIn:o,setToken:s,setUserInfo:r,login:async(e,a)=>{try{const t=await uni.request({url:`${me}/api/v1/auth/login`,method:"POST",data:{phone:e,code:a}});return 200===t.data.code?(s(t.data.data.token),r(t.data.data.user),{success:!0}):{success:!1,message:t.data.message}}catch(n){return t("error","at stores/user.js:51","登录失败:",n),{success:!1,message:"网络错误"}}},register:async(e,a)=>{try{const t=await uni.request({url:`${me}/api/v1/auth/register`,method:"POST",data:{phone:e,code:a}});return 200===t.data.code?(s(t.data.data.token),r(t.data.data.user),{success:!0}):{success:!1,message:t.data.message}}catch(n){return t("error","at stores/user.js:75","注册失败:",n),{success:!1,message:"网络错误"}}},getUserInfo:async()=>{if(a.value)try{const e=await uni.request({url:`${me}/api/v1/user/info`,method:"GET",header:{Authorization:`Bearer ${a.value}`}});200===e.data.code&&r(e.data.data)}catch(e){t("error","at stores/user.js:96","获取用户信息失败:",e)}},logout:()=>{a.value="",n.value={id:null,phone:"",nickname:"",balance_count:0,balance_duration:0,free_trial_count:3,ab_test_group:""},uni.removeStorageSync("token"),uni.removeStorageSync("userInfo")},sendSmsCode:async e=>{try{const t=await uni.request({url:`${me}/api/v1/auth/sms`,method:"POST",data:{phone:e}});return 200===t.data.code?{success:!0}:{success:!1,message:t.data.message}}catch(a){return t("error","at stores/user.js:129","发送验证码失败:",a),{success:!1,message:"网络错误"}}},initFromStorage:i}})),ve=(e,t)=>{const a=e.__vccOpts||e;for(const[n,o]of t)a[n]=o;return a};const ge=ve({__name:"index",setup(a,{expose:n}){n();const o=pe(),s=e.ref({});e.onMounted((()=>{r()}));const r=async()=>{try{await o.getUserInfo(),s.value=o.userInfo}catch(e){t("error","at pages/index/index.vue:65","获取用户信息失败:",e)}},i=()=>{o.isLoggedIn?uni.navigateTo({url:"/pages/payment/payment"}):uni.navigateTo({url:"/pages/login/login"})},c={userStore:o,userInfo:s,loadUserInfo:r,startInterview:()=>{o.isLoggedIn?s.value.balance_count<=0&&s.value.free_trial_count<=0?uni.showModal({title:"提示",content:"您的使用次数已用完，请购买套餐",success:e=>{e.confirm&&i()}}):uni.navigateTo({url:"/pages/interview/interview"}):uni.navigateTo({url:"/pages/login/login"})},goBuy:i,ref:e.ref,onMounted:e.onMounted,get useUserStore(){return pe}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}},[["render",function(t,a,n,o,s,r){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"面试助手"),e.createElementVNode("text",{class:"subtitle"},"AI助力，面试无忧")]),e.createElementVNode("view",{class:"stats-card"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(o.userInfo.balance_count||0),1),e.createElementVNode("text",{class:"stat-label"},"剩余次数")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(o.userInfo.free_trial_count||3),1),e.createElementVNode("text",{class:"stat-label"},"免费试用")])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"start-btn",onClick:o.startInterview},[e.createElementVNode("text",{class:"btn-text"},"开始面试")]),e.createElementVNode("button",{class:"buy-btn",onClick:o.goBuy},[e.createElementVNode("text",{class:"btn-text"},"购买套餐")])]),e.createElementVNode("view",{class:"features"},[e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"🎯"),e.createElementVNode("text",{class:"feature-title"},"实时回答"),e.createElementVNode("text",{class:"feature-desc"},"AI实时生成专业回答")]),e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"🔊"),e.createElementVNode("text",{class:"feature-title"},"语音播报"),e.createElementVNode("text",{class:"feature-desc"},"听筒私密播放答案")]),e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"📚"),e.createElementVNode("text",{class:"feature-title"},"领域定制"),e.createElementVNode("text",{class:"feature-desc"},"针对不同技术领域优化")])])])}],["__scopeId","data-v-1cf27b2a"],["__file","/Users/<USER>/code/interviewMaster/app/pages/index/index.vue"]]);const fe=ve({__name:"login",setup(t,{expose:a}){a();const n=pe(),o=e.ref({phone:"",code:""}),s=e.ref("login"),r=e.ref(!1),i=e.ref(0),c=e.ref(!1),l=e.computed((()=>i.value>0?`${i.value}s后重发`:"获取验证码")),d=e.computed((()=>11===o.value.phone.length&&6===o.value.code.length&&c.value)),u=()=>{r.value=!0,i.value=60;const e=setInterval((()=>{i.value--,i.value<=0&&(clearInterval(e),r.value=!1)}),1e3)};e.onMounted((()=>{n.isLoggedIn&&uni.switchTab({url:"/pages/index/index"})}));const m={userStore:n,form:o,currentTab:s,codeDisabled:r,codeCountdown:i,agreed:c,codeText:l,canSubmit:d,switchTab:e=>{s.value=e},sendCode:async()=>{if(o.value.phone)if(/^1[3-9]\d{9}$/.test(o.value.phone))try{const e="login"===s.value?2:1,t=await n.sendSmsCode(o.value.phone,e);t.success?(uni.showToast({title:"验证码已发送",icon:"success"}),u()):uni.showToast({title:t.message||"发送失败",icon:"none"})}catch(e){uni.showToast({title:"网络错误",icon:"none"})}else uni.showToast({title:"手机号格式不正确",icon:"none"});else uni.showToast({title:"请输入手机号",icon:"none"})},startCountdown:u,handleSubmit:async()=>{if(d.value){uni.showLoading({title:"login"===s.value?"登录中...":"注册中..."});try{let e;e="login"===s.value?await n.login(o.value.phone,o.value.code):await n.register(o.value.phone,o.value.code),uni.hideLoading(),e.success?(uni.showToast({title:"login"===s.value?"登录成功":"注册成功",icon:"success"}),setTimeout((()=>{uni.switchTab({url:"/pages/index/index"})}),1500)):uni.showToast({title:e.message||"操作失败",icon:"none"})}catch(e){uni.hideLoading(),uni.showToast({title:"网络错误",icon:"none"})}}},onAgreementChange:e=>{c.value=e.detail.value.includes("agree")},showAgreement:()=>{uni.showModal({title:"用户协议",content:"这里是用户协议内容...",showCancel:!1})},showPrivacy:()=>{uni.showModal({title:"隐私政策",content:"这里是隐私政策内容...",showCancel:!1})},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return pe}};return Object.defineProperty(m,"__isScriptSetup",{enumerable:!1,value:!0}),m}},[["render",function(t,a,n,o,s,r){return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"面试助手"),e.createElementVNode("text",{class:"subtitle"},"AI助力，面试无忧")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"手机号"),e.withDirectives(e.createElementVNode("input",{class:"input",type:"number",placeholder:"请输入手机号","onUpdate:modelValue":a[0]||(a[0]=e=>o.form.phone=e),maxlength:"11"},null,512),[[e.vModelText,o.form.phone]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"验证码"),e.createElementVNode("view",{class:"code-input-wrapper"},[e.withDirectives(e.createElementVNode("input",{class:"input code-input",type:"number",placeholder:"请输入验证码","onUpdate:modelValue":a[1]||(a[1]=e=>o.form.code=e),maxlength:"6"},null,512),[[e.vModelText,o.form.code]]),e.createElementVNode("button",{class:"code-btn",disabled:o.codeDisabled,onClick:o.sendCode},e.toDisplayString(o.codeText),9,["disabled"])])]),e.createElementVNode("view",{class:"tab-container"},[e.createElementVNode("view",{class:e.normalizeClass(["tab-item",{active:"login"===o.currentTab}]),onClick:a[2]||(a[2]=e=>o.switchTab("login"))}," 登录 ",2),e.createElementVNode("view",{class:e.normalizeClass(["tab-item",{active:"register"===o.currentTab}]),onClick:a[3]||(a[3]=e=>o.switchTab("register"))}," 注册 ",2)]),e.createElementVNode("button",{class:"submit-btn",disabled:!o.canSubmit,onClick:o.handleSubmit},e.toDisplayString("login"===o.currentTab?"登录":"注册"),9,["disabled"]),e.createElementVNode("view",{class:"agreement"},[e.createElementVNode("checkbox-group",{onChange:o.onAgreementChange},[e.createElementVNode("label",{class:"checkbox-label"},[e.createElementVNode("checkbox",{value:"agree",checked:o.agreed},null,8,["checked"]),e.createElementVNode("text",{class:"agreement-text"},[e.createTextVNode(" 我已阅读并同意 "),e.createElementVNode("text",{class:"link",onClick:o.showAgreement},"《用户协议》"),e.createTextVNode(" 和 "),e.createElementVNode("text",{class:"link",onClick:o.showPrivacy},"《隐私政策》")])])],32)])])])}],["__scopeId","data-v-e4e4508d"],["__file","/Users/<USER>/code/interviewMaster/app/pages/login/login.vue"]]),ye=ue("websocket",(()=>{const a=e.ref(null),n=e.ref(!1),o=e.ref("disconnected"),s=e.ref(null),r=e.ref(null),i=e.ref(null),c=e.ref(0),l=e.ref(5),d=e.ref("idle"),u=e.ref(""),m=e.computed((()=>n.value&&a.value)),p=()=>{if(a.value&&n.value)t("log","at stores/websocket.js:33","WebSocket已连接");else{o.value="connecting";try{a.value=uni.connectSocket({url:`ws://localhost:8080/ws/interview?token=${pe().token}`,success:()=>{t("log","at stores/websocket.js:43","WebSocket连接请求发送成功")},fail:e=>{t("error","at stores/websocket.js:46","WebSocket连接失败:",e),o.value="error",w()}}),a.value.onOpen((()=>{t("log","at stores/websocket.js:54","WebSocket连接已打开"),n.value=!0,o.value="connected",c.value=0,y()})),a.value.onMessage((e=>{g(e.data)})),a.value.onClose((()=>{t("log","at stores/websocket.js:68","WebSocket连接已关闭"),n.value=!1,o.value="disconnected",h(),w()})),a.value.onError((e=>{t("error","at stores/websocket.js:77","WebSocket错误:",e),o.value="error",w()}))}catch(e){t("error","at stores/websocket.js:83","创建WebSocket连接失败:",e),o.value="error",w()}}},v=e=>{if(!m.value)return t("error","at stores/websocket.js:104","WebSocket未连接，无法发送消息"),!1;try{const n="string"==typeof e?e:JSON.stringify(e);return a.value.send({data:n,success:()=>{t("log","at stores/websocket.js:113","消息发送成功:",n)},fail:e=>{t("error","at stores/websocket.js:116","消息发送失败:",e)}}),!0}catch(n){return t("error","at stores/websocket.js:121","发送消息异常:",n),!1}},g=e=>{try{const a=JSON.parse(e);switch(s.value=a,t("log","at stores/websocket.js:132","收到消息:",a),a.type){case"pong":break;case"status_update":d.value=a.status;break;case"audio_output":f(a.data);break;case"session_start":u.value=a.session_id;break;case"session_end":u.value="",d.value="idle";break;default:t("log","at stores/websocket.js:153","未知消息类型:",a.type)}}catch(a){t("error","at stores/websocket.js:156","解析消息失败:",a)}},f=e=>{t("log","at stores/websocket.js:163","播放音频:",e)},y=()=>{h(),r.value=setInterval((()=>{m.value&&v({type:"ping"})}),3e4)},h=()=>{r.value&&(clearInterval(r.value),r.value=null)},w=()=>{if(c.value>=l.value)return void t("log","at stores/websocket.js:201","达到最大重连次数，停止重连");b();const e=Math.min(1e3*Math.pow(2,c.value),3e4);t("log","at stores/websocket.js:208",`${e}ms后尝试重连...`),i.value=setTimeout((()=>{c.value++,p()}),e)},b=()=>{i.value&&(clearTimeout(i.value),i.value=null)};return{socket:a,isConnected:n,connectionStatus:o,lastMessage:s,interviewStatus:d,currentSessionId:u,canSendMessage:m,connect:p,disconnect:()=>{a.value&&(a.value.close(),a.value=null),n.value=!1,o.value="disconnected",h(),b()},sendMessage:v,sendAudio:e=>v({type:"audio_input",data:e}),sendInterrupt:()=>v({type:"interrupt"}),handleMessage:g}}));const he=ve({__name:"interview",setup(a,{expose:n}){n();const o=pe(),s=ye(),r=e.ref(!1),i=e.ref(""),c=e.ref("idle"),l=e.ref(!1),d=e.ref(!1),u=e.ref(1),m=e.ref([]),p=uni.getRecorderManager(),v=uni.createInnerAudioContext(),g=e.computed((()=>({idle:"空闲",connecting:"连接中...",listening:"正在聆听...",thinking:"AI思考中...",speaking:"正在播报...",error:"连接错误"}[c.value]||"未知状态"))),f=e.computed((()=>`status-${c.value}`)),y=e.computed((()=>r.value?"🎤":"thinking"===c.value?"🤔":"speaking"===c.value?"🔊":"🎯")),h=e.computed((()=>r.value?"松开结束":"thinking"===c.value?"AI思考中":"speaking"===c.value?"正在播报":"按住说话")),w=e.computed((()=>s.isConnected&&("idle"===c.value||"listening"===c.value))),b=()=>{d.value=!1,uni.setStorageSync("guide_shown",!0)};e.onMounted((async()=>{if(!o.isLoggedIn)return void uni.redirectTo({url:"/pages/login/login"});uni.getStorageSync("guide_shown")||(d.value=!0),c.value="connecting",s.connect(),s.$subscribe(((e,t)=>{"websocket"===e.storeId&&(c.value=t.interviewStatus,i.value=t.currentSessionId)})),p.onStart((()=>{t("log","at pages/interview/interview.vue:317","录音开始")})),p.onStop((e=>{t("log","at pages/interview/interview.vue:321","录音结束",e),e.tempFilePath&&s.sendAudio(e.tempFilePath)})),p.onError((e=>{t("error","at pages/interview/interview.vue:329","录音错误",e),r.value=!1,c.value="idle",uni.showToast({title:"录音失败",icon:"none"})})),v.onPlay((()=>{t("log","at pages/interview/interview.vue:340","音频播放开始")})),v.onEnded((()=>{t("log","at pages/interview/interview.vue:344","音频播放结束"),c.value="idle"})),v.onError((e=>{t("error","at pages/interview/interview.vue:349","音频播放错误",e),c.value="idle"}))})),e.onUnmounted((()=>{p.stop(),v.destroy(),s.disconnect()}));const E={userStore:o,wsStore:s,isRecording:r,sessionId:i,interviewStatus:c,showHistory:l,showGuide:d,guideStep:u,recentHistory:m,recorderManager:p,innerAudioContext:v,statusText:g,statusClass:f,buttonIcon:y,buttonText:h,canRecord:w,startRecording:()=>{w.value&&uni.authorize({scope:"scope.record",success(){r.value=!0,c.value="listening",p.start({duration:6e4,sampleRate:16e3,numberOfChannels:1,encodeBitRate:48e3,format:"wav"})},fail(){uni.showModal({title:"权限申请",content:"需要录音权限才能使用面试功能",success:e=>{e.confirm&&uni.openSetting()}})}})},stopRecording:()=>{r.value&&(r.value=!1,c.value="thinking",p.stop())},interruptAnswer:()=>{s.sendInterrupt(),v.stop(),c.value="idle"},endInterview:()=>{uni.showModal({title:"确认",content:"确定要结束本次面试吗？",success:e=>{e.confirm&&(s.disconnect(),uni.navigateBack())}})},toggleHistory:()=>{l.value=!l.value},giveFeedback:(e,t)=>{const a=m.value.find((t=>t.id===e));a&&(a.feedback=a.feedback===t?0:t)},formatTime:e=>{const t=new Date(e);return`${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`},nextGuide:()=>{u.value<3?u.value++:b()},closeGuide:b,ref:e.ref,computed:e.computed,onMounted:e.onMounted,onUnmounted:e.onUnmounted,get useUserStore(){return pe},get useWebSocketStore(){return ye}};return Object.defineProperty(E,"__isScriptSetup",{enumerable:!1,value:!0}),E}},[["render",function(t,a,n,o,s,r){return e.openBlock(),e.createElementBlock("view",{class:"interview-container"},[e.createCommentVNode(" 状态栏 "),e.createElementVNode("view",{class:"status-bar"},[e.createElementVNode("view",{class:"status-info"},[e.createElementVNode("text",{class:"status-text"},e.toDisplayString(o.statusText),1),e.createElementVNode("view",{class:e.normalizeClass(["status-indicator",o.statusClass])},null,2)]),e.createElementVNode("view",{class:"session-info"},[e.createElementVNode("text",{class:"session-text"},"会话: "+e.toDisplayString(o.sessionId||"未连接"),1)])]),e.createCommentVNode(" 主控制区域 "),e.createElementVNode("view",{class:"control-area"},[e.createCommentVNode(" 中心按钮 "),e.createElementVNode("view",{class:"center-button-container"},[e.createElementVNode("button",{class:e.normalizeClass(["center-button",{recording:o.isRecording,disabled:!o.canRecord}]),onTouchstart:o.startRecording,onTouchend:o.stopRecording,onTouchcancel:o.stopRecording},[e.createElementVNode("view",{class:"button-icon"},[e.createElementVNode("text",{class:"icon"},e.toDisplayString(o.buttonIcon),1)]),e.createElementVNode("text",{class:"button-text"},e.toDisplayString(o.buttonText),1)],34),e.createCommentVNode(" 录音动画 "),o.isRecording?(e.openBlock(),e.createElementBlock("view",{key:0,class:"recording-animation"},[e.createElementVNode("view",{class:"wave wave1"}),e.createElementVNode("view",{class:"wave wave2"}),e.createElementVNode("view",{class:"wave wave3"})])):e.createCommentVNode("v-if",!0)]),e.createCommentVNode(" 控制按钮组 "),e.createElementVNode("view",{class:"control-buttons"},["speaking"===o.interviewStatus?(e.openBlock(),e.createElementBlock("button",{key:0,class:"control-btn interrupt-btn",onClick:o.interruptAnswer},[e.createElementVNode("text",{class:"btn-icon"},"⏸"),e.createElementVNode("text",{class:"btn-text"},"打断")])):e.createCommentVNode("v-if",!0),e.createElementVNode("button",{class:"control-btn end-btn",onClick:o.endInterview},[e.createElementVNode("text",{class:"btn-icon"},"⏹"),e.createElementVNode("text",{class:"btn-text"},"结束")])])]),e.createCommentVNode(" 历史记录 "),e.createElementVNode("view",{class:"history-section"},[e.createElementVNode("view",{class:"history-header",onClick:o.toggleHistory},[e.createElementVNode("text",{class:"history-title"},"历史记录"),e.createElementVNode("text",{class:"history-toggle"},e.toDisplayString(o.showHistory?"收起":"展开"),1)]),o.showHistory?(e.openBlock(),e.createElementBlock("view",{key:0,class:"history-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.recentHistory,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"history-item"},[e.createElementVNode("view",{class:"question"},[e.createElementVNode("text",{class:"label"},"问题:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.question),1)]),e.createElementVNode("view",{class:"answer"},[e.createElementVNode("text",{class:"label"},"回答:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.answer),1)]),e.createElementVNode("view",{class:"meta"},[e.createElementVNode("text",{class:"time"},e.toDisplayString(o.formatTime(t.time)),1),e.createElementVNode("view",{class:"feedback"},[e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:1===t.feedback}]),onClick:e=>o.giveFeedback(t.id,1)}," 👍 ",10,["onClick"]),e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:-1===t.feedback}]),onClick:e=>o.giveFeedback(t.id,-1)}," 👎 ",10,["onClick"])])])])))),128))])):e.createCommentVNode("v-if",!0)]),e.createCommentVNode(" 新手引导 "),o.showGuide?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guide-overlay",onClick:o.closeGuide},[e.createElementVNode("view",{class:"guide-content",onClick:a[0]||(a[0]=e.withModifiers((()=>{}),["stop"]))},[1===o.guideStep?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"欢迎使用面试助手"),e.createElementVNode("text",{class:"guide-text"},"按住中心按钮说话，AI会实时为您提供专业的回答建议"),e.createElementVNode("button",{class:"guide-btn",onClick:o.nextGuide},"下一步")])):e.createCommentVNode("v-if",!0),2===o.guideStep?(e.openBlock(),e.createElementBlock("view",{key:1,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"听筒播放"),e.createElementVNode("text",{class:"guide-text"},"答案会通过听筒播放，请将手机靠近耳朵以获得最佳体验"),e.createElementVNode("button",{class:"guide-btn",onClick:o.nextGuide},"下一步")])):e.createCommentVNode("v-if",!0),3===o.guideStep?(e.openBlock(),e.createElementBlock("view",{key:2,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"打断功能"),e.createElementVNode("text",{class:"guide-text"},'在AI回答时，您可以点击"打断"按钮来停止当前回答'),e.createElementVNode("button",{class:"guide-btn",onClick:o.closeGuide},"开始使用")])):e.createCommentVNode("v-if",!0)])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-99492b0e"],["__file","/Users/<USER>/code/interviewMaster/app/pages/interview/interview.vue"]]);const we=ve({__name:"history",setup(a,{expose:n}){n();const o=pe(),s=e.ref([]),r=e.ref({}),i=e.ref(!1),c=e.ref(!0),l=e.ref(1),d=e.ref(20),u=e.ref(""),m=e.ref(!1),p=e.ref(null),v=async(e=!1)=>{if(!i.value){i.value=!0;try{const t={page:e?1:l.value,page_size:d.value};u.value&&(t.date=u.value);const a={list:[{id:1,session_id:"session_1234567890",question_text:"请介绍一下JavaScript的闭包概念",answer_text:"闭包是JavaScript中的一个重要概念，它指的是函数能够访问其外部作用域中的变量，即使在函数外部调用时也是如此...",response_time_ms:1200,user_feedback:1,prompt_version:"A",created_at:(new Date).toISOString()}],total:1,page:1,page_size:20};e?(s.value=a.list,l.value=1):s.value.push(...a.list),c.value=s.value.length<a.total,l.value++}catch(a){t("error","at pages/history/history.vue:218","加载历史记录失败:",a),uni.showToast({title:"加载失败",icon:"none"})}finally{i.value=!1}}},g=async()=>{try{r.value={total_count:25,month_count:8,today_count:2,avg_response_time:1350}}catch(e){t("error","at pages/history/history.vue:242","加载统计信息失败:",e)}};e.onMounted((()=>{o.isLoggedIn?(v(!0),g()):uni.redirectTo({url:"/pages/login/login"})}));const f={userStore:o,historyList:s,stats:r,loading:i,hasMore:c,page:l,pageSize:d,filterDate:u,showDetailModal:m,selectedItem:p,loadHistory:v,loadStats:g,loadMore:()=>{c.value&&!i.value&&v(!1)},onDateChange:e=>{u.value=e.detail.value,v(!0)},clearFilter:()=>{u.value="",v(!0)},giveFeedback:async(e,a)=>{try{const t=s.value.find((t=>t.id===e));t&&(t.user_feedback=t.user_feedback===a?0:a),uni.showToast({title:"反馈成功",icon:"success"})}catch(n){t("error","at pages/history/history.vue:280","反馈失败:",n),uni.showToast({title:"反馈失败",icon:"none"})}},showDetail:e=>{p.value=e,m.value=!0},closeDetail:()=>{m.value=!1,p.value=null},startInterview:()=>{uni.navigateTo({url:"/pages/interview/interview"})},formatDateTime:e=>{if(!e)return"";const t=new Date(e);return`${t.getMonth()+1}/${t.getDate()} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`},formatResponseTime:e=>e?e<1e3?`${e}ms`:`${(e/1e3).toFixed(1)}s`:"0ms",truncateText:(e,t)=>e?e.length<=t?e:e.substring(0,t)+"...":"",ref:e.ref,onMounted:e.onMounted,get useUserStore(){return pe}};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}},[["render",function(t,a,n,o,s,r){var i,c,l,d,u,m;return e.openBlock(),e.createElementBlock("view",{class:"history-container"},[e.createCommentVNode(" 筛选栏 "),e.createElementVNode("view",{class:"filter-bar"},[e.createElementVNode("picker",{mode:"date",value:o.filterDate,onChange:o.onDateChange,class:"date-picker"},[e.createElementVNode("view",{class:"picker-text"},e.toDisplayString(o.filterDate||"选择日期"),1)],40,["value"]),e.createElementVNode("button",{class:"clear-filter",onClick:o.clearFilter}," 清除筛选 ")]),e.createCommentVNode(" 统计信息 "),e.createElementVNode("view",{class:"stats-card"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(o.stats.total_count||0),1),e.createElementVNode("text",{class:"stat-label"},"总次数")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(o.stats.month_count||0),1),e.createElementVNode("text",{class:"stat-label"},"本月")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(o.stats.today_count||0),1),e.createElementVNode("text",{class:"stat-label"},"今日")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(o.formatResponseTime(o.stats.avg_response_time)),1),e.createElementVNode("text",{class:"stat-label"},"平均响应")])]),e.createCommentVNode(" 历史记录列表 "),e.createElementVNode("view",{class:"history-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.historyList,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"history-item",onClick:e=>o.showDetail(t)},[e.createElementVNode("view",{class:"item-header"},[e.createElementVNode("text",{class:"session-id"},"会话 "+e.toDisplayString(t.session_id.slice(-8)),1),e.createElementVNode("text",{class:"time"},e.toDisplayString(o.formatDateTime(t.created_at)),1)]),e.createElementVNode("view",{class:"question-section"},[e.createElementVNode("text",{class:"label"},"问题:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.question_text||"语音问题"),1)]),e.createElementVNode("view",{class:"answer-section"},[e.createElementVNode("text",{class:"label"},"回答:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(o.truncateText(t.answer_text,100)),1)]),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"meta-info"},[e.createElementVNode("text",{class:"response-time"},"响应: "+e.toDisplayString(t.response_time_ms)+"ms",1),e.createElementVNode("text",{class:"prompt-version"},"版本: "+e.toDisplayString(t.prompt_version),1)]),e.createElementVNode("view",{class:"feedback-buttons"},[e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:1===t.user_feedback}]),onClick:e.withModifiers((e=>o.giveFeedback(t.id,1)),["stop"])}," 👍 ",10,["onClick"]),e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:-1===t.user_feedback}]),onClick:e.withModifiers((e=>o.giveFeedback(t.id,-1)),["stop"])}," 👎 ",10,["onClick"])])])],8,["onClick"])))),128)),e.createCommentVNode(" 加载更多 "),o.hasMore?(e.openBlock(),e.createElementBlock("view",{key:0,class:"load-more",onClick:o.loadMore},[e.createElementVNode("text",{class:"load-text"},e.toDisplayString(o.loading?"加载中...":"加载更多"),1)])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 空状态 "),o.loading||0!==o.historyList.length?e.createCommentVNode("v-if",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"empty-state"},[e.createElementVNode("text",{class:"empty-icon"},"📝"),e.createElementVNode("text",{class:"empty-text"},"暂无面试记录"),e.createElementVNode("button",{class:"start-btn",onClick:o.startInterview}," 开始面试 ")]))]),e.createCommentVNode(" 详情弹窗 "),o.showDetailModal?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-modal",onClick:o.closeDetail},[e.createElementVNode("view",{class:"detail-content",onClick:a[0]||(a[0]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"detail-header"},[e.createElementVNode("text",{class:"detail-title"},"面试记录详情"),e.createElementVNode("button",{class:"close-btn",onClick:o.closeDetail},"×")]),e.createElementVNode("view",{class:"detail-body"},[e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"会话ID:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(i=o.selectedItem)?void 0:i.session_id),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"时间:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(o.formatDateTime(null==(c=o.selectedItem)?void 0:c.created_at)),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"问题:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString((null==(l=o.selectedItem)?void 0:l.question_text)||"语音问题"),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"回答:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(d=o.selectedItem)?void 0:d.answer_text),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"响应时间:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(u=o.selectedItem)?void 0:u.response_time_ms)+"ms",1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"提示词版本:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(m=o.selectedItem)?void 0:m.prompt_version),1)])])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-b2d018fa"],["__file","/Users/<USER>/code/interviewMaster/app/pages/history/history.vue"]]);const be=ve({__name:"profile",setup(a,{expose:n}){n();const o=pe(),s=e.computed((()=>o.userInfo)),r=e.ref(!1),i=e.ref({nickname:"",avatar:""}),c=()=>{r.value=!1};e.onMounted((async()=>{if(o.isLoggedIn)try{await o.getUserInfo()}catch(e){t("error","at pages/profile/profile.vue:278","获取用户信息失败:",e)}else uni.redirectTo({url:"/pages/login/login"})}));const l={userStore:o,userInfo:s,showEditModal:r,editForm:i,editProfile:()=>{i.value={nickname:s.value.nickname||"",avatar:s.value.avatar||""},r.value=!0},closeEdit:c,chooseAvatar:()=>{uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:e=>{i.value.avatar=e.tempFilePaths[0]}})},saveProfile:async()=>{try{uni.showLoading({title:"保存中..."}),o.setUserInfo(i.value),uni.hideLoading(),uni.showToast({title:"保存成功",icon:"success"}),c()}catch(e){uni.hideLoading(),uni.showToast({title:"保存失败",icon:"none"})}},goRecharge:()=>{uni.navigateTo({url:"/pages/payment/payment"})},goHistory:()=>{uni.switchTab({url:"/pages/history/history"})},goOrders:()=>{uni.navigateTo({url:"/pages/orders/orders"})},goSettings:()=>{uni.navigateTo({url:"/pages/settings/settings"})},goHelp:()=>{uni.navigateTo({url:"/pages/help/help"})},goAbout:()=>{uni.navigateTo({url:"/pages/about/about"})},logout:()=>{uni.showModal({title:"确认",content:"确定要退出登录吗？",success:e=>{e.confirm&&(o.logout(),uni.reLaunch({url:"/pages/login/login"}))}})},formatDuration:e=>{if(!e)return"0分钟";const t=Math.floor(e/3600),a=Math.floor(e%3600/60);return t>0?`${t}小时${a}分钟`:`${a}分钟`},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return pe}};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}},[["render",function(t,a,n,o,s,r){return e.openBlock(),e.createElementBlock("view",{class:"profile-container"},[e.createCommentVNode(" 用户信息卡片 "),e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"avatar-section"},[e.createElementVNode("image",{class:"avatar",src:o.userInfo.avatar||"/static/default-avatar.png"},null,8,["src"]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"nickname"},e.toDisplayString(o.userInfo.nickname||"面试者"),1),e.createElementVNode("text",{class:"phone"},e.toDisplayString(o.userInfo.phone),1)])]),e.createElementVNode("button",{class:"edit-btn",onClick:o.editProfile}," 编辑 ")]),e.createCommentVNode(" 余额信息 "),e.createElementVNode("view",{class:"balance-card"},[e.createElementVNode("view",{class:"balance-header"},[e.createElementVNode("text",{class:"balance-title"},"我的余额"),e.createElementVNode("button",{class:"recharge-btn",onClick:o.goRecharge}," 充值 ")]),e.createElementVNode("view",{class:"balance-info"},[e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(o.userInfo.balance_count||0),1),e.createElementVNode("text",{class:"balance-label"},"剩余次数")]),e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(o.userInfo.free_trial_count||0),1),e.createElementVNode("text",{class:"balance-label"},"免费试用")]),e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(o.formatDuration(o.userInfo.balance_duration)),1),e.createElementVNode("text",{class:"balance-label"},"剩余时长")])])]),e.createCommentVNode(" 功能菜单 "),e.createElementVNode("view",{class:"menu-section"},[e.createElementVNode("view",{class:"menu-item",onClick:o.goHistory},[e.createElementVNode("view",{class:"menu-icon"},"📊"),e.createElementVNode("text",{class:"menu-text"},"面试历史"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:o.goOrders},[e.createElementVNode("view",{class:"menu-icon"},"📋"),e.createElementVNode("text",{class:"menu-text"},"我的订单"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:o.goSettings},[e.createElementVNode("view",{class:"menu-icon"},"⚙️"),e.createElementVNode("text",{class:"menu-text"},"设置"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:o.goHelp},[e.createElementVNode("view",{class:"menu-icon"},"❓"),e.createElementVNode("text",{class:"menu-text"},"帮助与反馈"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:o.goAbout},[e.createElementVNode("view",{class:"menu-icon"},"ℹ️"),e.createElementVNode("text",{class:"menu-text"},"关于我们"),e.createElementVNode("text",{class:"menu-arrow"},">")])]),e.createCommentVNode(" 退出登录 "),e.createElementVNode("view",{class:"logout-section"},[e.createElementVNode("button",{class:"logout-btn",onClick:o.logout}," 退出登录 ")]),e.createCommentVNode(" 编辑资料弹窗 "),o.showEditModal?(e.openBlock(),e.createElementBlock("view",{key:0,class:"edit-modal",onClick:o.closeEdit},[e.createElementVNode("view",{class:"edit-content",onClick:a[1]||(a[1]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"edit-header"},[e.createElementVNode("text",{class:"edit-title"},"编辑资料"),e.createElementVNode("button",{class:"close-btn",onClick:o.closeEdit},"×")]),e.createElementVNode("view",{class:"edit-body"},[e.createElementVNode("view",{class:"edit-item"},[e.createElementVNode("text",{class:"edit-label"},"昵称"),e.withDirectives(e.createElementVNode("input",{class:"edit-input","onUpdate:modelValue":a[0]||(a[0]=e=>o.editForm.nickname=e),placeholder:"请输入昵称",maxlength:"20"},null,512),[[e.vModelText,o.editForm.nickname]])]),e.createElementVNode("view",{class:"edit-item"},[e.createElementVNode("text",{class:"edit-label"},"头像"),e.createElementVNode("view",{class:"avatar-upload",onClick:o.chooseAvatar},[e.createElementVNode("image",{class:"upload-avatar",src:o.editForm.avatar||"/static/default-avatar.png"},null,8,["src"]),e.createElementVNode("text",{class:"upload-text"},"点击更换")])])]),e.createElementVNode("view",{class:"edit-footer"},[e.createElementVNode("button",{class:"cancel-btn",onClick:o.closeEdit},"取消"),e.createElementVNode("button",{class:"save-btn",onClick:o.saveProfile},"保存")])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-dd383ca2"],["__file","/Users/<USER>/code/interviewMaster/app/pages/profile/profile.vue"]]);const Ee=ve({__name:"payment",setup(a,{expose:n}){n();const o=pe(),s=e.ref([]),r=e.ref([]),i=e.ref(null),c=e.ref(""),l=e.ref(!1),d=e.ref(!1),u=e.ref(null),m=e.computed((()=>{const e=r.value.find((e=>e.type===c.value));return(null==e?void 0:e.name)||""})),p=async()=>{try{s.value=[{id:"trial_10",name:"新手体验包",description:"10次面试机会，适合初次体验",price:9.9,count:10,duration:0,type:1},{id:"standard_50",name:"标准套餐",description:"50次面试机会，适合求职准备",price:39.9,count:50,duration:0,type:1},{id:"premium_100",name:"高级套餐",description:"100次面试机会，适合长期使用",price:69.9,count:100,duration:0,type:1},{id:"monthly_unlimited",name:"包月畅享",description:"一个月内无限次使用",price:99.9,count:0,duration:2592e3,type:3}]}catch(e){t("error","at pages/payment/payment.vue:219","加载商品列表失败:",e),uni.showToast({title:"加载失败",icon:"none"})}},v=async()=>{try{r.value=[{type:"wechat",name:"微信支付",icon:"wechat",description:"使用微信扫码支付",enabled:!0},{type:"alipay",name:"支付宝",icon:"alipay",description:"使用支付宝扫码支付",enabled:!0}]}catch(e){t("error","at pages/payment/payment.vue:251","加载支付方式失败:",e)}},g=async()=>{try{return{success:!0,data:{order_no:"IM"+Date.now(),product_id:i.value.id,amount:i.value.price,status:0}}}catch(e){return{success:!1,message:"创建订单失败"}}},f=async e=>{try{return{success:!0,data:{code_url:"weixin://wxpay/bizpayurl?pr=mock_code"}}}catch(t){return{success:!1,message:"创建微信支付失败"}}},y=async e=>{try{return{success:!0,data:{qr_code:"https://qr.alipay.com/mock_code"}}}catch(t){return{success:!1,message:"创建支付宝支付失败"}}},h=()=>{const e=setInterval((async()=>{const t=await w();"paid"===t?(clearInterval(e),b()):"failed"===t&&(clearInterval(e),E())}),3e3);setTimeout((()=>{clearInterval(e)}),3e5)},w=async()=>{if(!u.value)return"pending";try{return Math.random()>.8?"paid":"pending"}catch(e){return t("error","at pages/payment/payment.vue:421","检查支付状态失败:",e),"pending"}},b=()=>{V(),uni.showToast({title:"支付成功",icon:"success"}),o.getUserInfo(),setTimeout((()=>{uni.navigateBack()}),2e3)},E=()=>{V(),uni.showToast({title:"支付失败",icon:"none"})},V=()=>{d.value=!1};e.onMounted((()=>{o.isLoggedIn?(p(),v()):uni.redirectTo({url:"/pages/login/login"})}));const N={userStore:o,products:s,paymentMethods:r,selectedProduct:i,selectedPayment:c,paying:l,showQRCode:d,currentOrder:u,paymentMethodName:m,loadProducts:p,loadPaymentMethods:v,selectProduct:e=>{i.value=e,r.value.length>0&&(c.value=r.value[0].type)},selectPayment:e=>{const t=r.value.find((t=>t.type===e));t&&t.enabled&&(c.value=e)},createPayment:async()=>{if(i.value&&c.value){l.value=!0;try{const e=await g();if(!e.success)throw new Error(e.message);let t;if(u.value=e.data,"wechat"===c.value?t=await f(u.value.order_no):"alipay"===c.value&&(t=await y(u.value.order_no)),!t.success)throw new Error(t.message);d.value=!0,h()}catch(e){t("error","at pages/payment/payment.vue:306","创建支付失败:",e),uni.showToast({title:e.message||"支付失败",icon:"none"})}finally{l.value=!1}}},createOrder:g,createWeChatPayment:f,createAlipayPayment:y,startPaymentPolling:h,checkPaymentStatus:w,handlePaymentSuccess:b,handlePaymentFailed:E,closeQRCode:V,formatDuration:e=>{if(!e)return"0分钟";const t=Math.floor(e/86400),a=Math.floor(e%86400/3600),n=Math.floor(e%3600/60);return t>0?`${t}天`:a>0?`${a}小时`:`${n}分钟`},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return pe}};return Object.defineProperty(N,"__isScriptSetup",{enumerable:!1,value:!0}),N}},[["render",function(t,a,n,o,s,r){var i;return e.openBlock(),e.createElementBlock("view",{class:"payment-container"},[e.createCommentVNode(" 商品列表 "),e.createElementVNode("view",{class:"products-section"},[e.createElementVNode("text",{class:"section-title"},"选择套餐"),e.createElementVNode("view",{class:"products-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.products,(t=>{var a,n;return e.openBlock(),e.createElementBlock("view",{key:t.id,class:e.normalizeClass(["product-item",{selected:(null==(a=o.selectedProduct)?void 0:a.id)===t.id}]),onClick:e=>o.selectProduct(t)},[e.createElementVNode("view",{class:"product-header"},[e.createElementVNode("text",{class:"product-name"},e.toDisplayString(t.name),1),"trial_10"===t.id?(e.openBlock(),e.createElementBlock("view",{key:0,class:"product-badge"}," 推荐 ")):e.createCommentVNode("v-if",!0)]),e.createElementVNode("text",{class:"product-desc"},e.toDisplayString(t.description),1),e.createElementVNode("view",{class:"product-info"},[e.createElementVNode("view",{class:"product-details"},[t.count>0?(e.openBlock(),e.createElementBlock("text",{key:0,class:"detail-item"},e.toDisplayString(t.count)+"次面试机会 ",1)):e.createCommentVNode("v-if",!0),t.duration>0?(e.openBlock(),e.createElementBlock("text",{key:1,class:"detail-item"},e.toDisplayString(o.formatDuration(t.duration))+"使用时长 ",1)):e.createCommentVNode("v-if",!0)]),e.createElementVNode("view",{class:"product-price"},[e.createElementVNode("text",{class:"price-symbol"},"¥"),e.createElementVNode("text",{class:"price-amount"},e.toDisplayString(t.price),1)])]),(null==(n=o.selectedProduct)?void 0:n.id)===t.id?(e.openBlock(),e.createElementBlock("view",{key:0,class:"select-indicator"}," ✓ ")):e.createCommentVNode("v-if",!0)],10,["onClick"])})),128))])]),e.createCommentVNode(" 支付方式 "),o.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:0,class:"payment-methods-section"},[e.createElementVNode("text",{class:"section-title"},"支付方式"),e.createElementVNode("view",{class:"payment-methods"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.paymentMethods,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.type,class:e.normalizeClass(["payment-method",{selected:o.selectedPayment===t.type,disabled:!t.enabled}]),onClick:e=>o.selectPayment(t.type)},[e.createElementVNode("view",{class:"method-icon"},["wechat"===t.type?(e.openBlock(),e.createElementBlock("text",{key:0},"💚")):e.createCommentVNode("v-if",!0),"alipay"===t.type?(e.openBlock(),e.createElementBlock("text",{key:1},"💙")):e.createCommentVNode("v-if",!0)]),e.createElementVNode("view",{class:"method-info"},[e.createElementVNode("text",{class:"method-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"method-desc"},e.toDisplayString(t.description),1)]),e.createElementVNode("view",{class:"method-radio"},[e.createElementVNode("view",{class:e.normalizeClass(["radio-circle",{checked:o.selectedPayment===t.type}])},null,2)])],10,["onClick"])))),128))])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 订单信息 "),o.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:1,class:"order-info-section"},[e.createElementVNode("text",{class:"section-title"},"订单信息"),e.createElementVNode("view",{class:"order-details"},[e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"商品名称:"),e.createElementVNode("text",{class:"order-value"},e.toDisplayString(o.selectedProduct.name),1)]),e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"商品价格:"),e.createElementVNode("text",{class:"order-value"},"¥"+e.toDisplayString(o.selectedProduct.price),1)]),e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"优惠金额:"),e.createElementVNode("text",{class:"order-value"},"¥0.00")]),e.createElementVNode("view",{class:"order-item total"},[e.createElementVNode("text",{class:"order-label"},"实付金额:"),e.createElementVNode("text",{class:"order-value price"},"¥"+e.toDisplayString(o.selectedProduct.price),1)])])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 支付按钮 "),o.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:2,class:"pay-button-section"},[e.createElementVNode("button",{class:"pay-button",disabled:!o.selectedPayment||o.paying,onClick:o.createPayment},e.toDisplayString(o.paying?"处理中...":`立即支付 ¥${o.selectedProduct.price}`),9,["disabled"])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 支付二维码弹窗 "),o.showQRCode?(e.openBlock(),e.createElementBlock("view",{key:3,class:"qr-modal",onClick:o.closeQRCode},[e.createElementVNode("view",{class:"qr-content",onClick:a[0]||(a[0]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"qr-header"},[e.createElementVNode("text",{class:"qr-title"},"扫码支付"),e.createElementVNode("button",{class:"close-btn",onClick:o.closeQRCode},"×")]),e.createElementVNode("view",{class:"qr-body"},[e.createElementVNode("view",{class:"qr-code"},[e.createCommentVNode(" 这里应该显示二维码图片 "),e.createElementVNode("text",{class:"qr-placeholder"},"二维码")]),e.createElementVNode("text",{class:"qr-tip"},"请使用"+e.toDisplayString(o.paymentMethodName)+"扫描二维码完成支付",1),e.createElementVNode("view",{class:"qr-amount"},[e.createElementVNode("text",{class:"amount-label"},"支付金额:"),e.createElementVNode("text",{class:"amount-value"},"¥"+e.toDisplayString(null==(i=o.selectedProduct)?void 0:i.price),1)])]),e.createElementVNode("view",{class:"qr-footer"},[e.createElementVNode("button",{class:"check-btn",onClick:o.checkPaymentStatus}," 检查支付状态 ")])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-eade9ab2"],["__file","/Users/<USER>/code/interviewMaster/app/pages/payment/payment.vue"]]);__definePage("pages/index/index",ge),__definePage("pages/login/login",fe),__definePage("pages/interview/interview",he),__definePage("pages/history/history",we),__definePage("pages/profile/profile",be),__definePage("pages/payment/payment",Ee);const Ve=ve({__name:"App",setup(e,{expose:a}){a(),s((()=>{t("log","at App.vue:12","App Launch"),r()})),n((()=>{t("log","at App.vue:18","App Show")})),o((()=>{t("log","at App.vue:23","App Hide")}));const r=()=>{const e=uni.getStorageSync("token");e&&i(e)},i=e=>{t("log","at App.vue:39","验证token:",e)},c={initApp:r,validateToken:i,get onLaunch(){return s},get onShow(){return n},get onHide(){return o}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}},[["render",function(t,a,n,o,s,r){return e.openBlock(),e.createElementBlock("view",{id:"app"},[e.createCommentVNode(" 应用根组件 ")])}],["__file","/Users/<USER>/code/interviewMaster/app/App.vue"]]);const{app:Ne,Vuex:ke,Pinia:_e}=function(){const t=e.createVueApp(Ve),a=function(){const t=e.effectScope(!0),a=t.run((()=>e.ref({})));let n=[],o=[];const s=e.markRaw({install(e){f(s),s._a=e,e.provide(y,s),e.config.globalProperties.$pinia=s,V&&Q(e,s),o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:t,_s:new Map,state:a});return V&&"undefined"!=typeof Proxy&&s.use(Z),s}();return t.use(a),{app:t,pinia:a}}();uni.Vuex=ke,uni.Pinia=_e,Ne.provide("__globalStyles",__uniConfig.styles),Ne._component.mpType="app",Ne._component.render=()=>{},Ne.mount("#app")}(Vue);
