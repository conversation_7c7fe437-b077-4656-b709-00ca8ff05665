import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { BASE_URL } from '../config'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref('')
  const userInfo = ref({
    id: null,
    phone: '',
    nickname: '',
    balance_count: 0,
    balance_duration: 0,
    free_trial_count: 3,
    ab_test_group: ''
  })
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  
  // 动作
  const setToken = (newToken) => {
    token.value = newToken
    uni.setStorageSync('token', newToken)
  }
  
  const setUserInfo = (info) => {
    userInfo.value = { ...userInfo.value, ...info }
    uni.setStorageSync('userInfo', userInfo.value)
  }
  
  const login = async (phone, code) => {
    try {
      const response = await uni.request({
        url: `${BASE_URL}/api/v1/auth/login`,
        method: 'POST',
        data: {
          phone,
          code
        }
      })
      
      if (response.data.code === 200) {
        setToken(response.data.data.token)
        setUserInfo(response.data.data.user)
        return { success: true }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, message: '网络错误' }
    }
  }
  
  const register = async (phone, code) => {
    try {
      const response = await uni.request({
        url: `${BASE_URL}/api/v1/auth/register`,
        method: 'POST',
        data: {
          phone,
          code
        }
      })
      
      if (response.data.code === 200) {
        setToken(response.data.data.token)
        setUserInfo(response.data.data.user)
        return { success: true }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('注册失败:', error)
      return { success: false, message: '网络错误' }
    }
  }
  
  const getUserInfo = async () => {
    if (!token.value) return
    
    try {
      const response = await uni.request({
        url: `${BASE_URL}/api/v1/user/info`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token.value}`
        }
      })
      
      if (response.data.code === 200) {
        setUserInfo(response.data.data)
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
  
  const logout = () => {
    token.value = ''
    userInfo.value = {
      id: null,
      phone: '',
      nickname: '',
      balance_count: 0,
      balance_duration: 0,
      free_trial_count: 3,
      ab_test_group: ''
    }
    uni.removeStorageSync('token')
    uni.removeStorageSync('userInfo')
  }
  
  const sendSmsCode = async (phone) => {
    try {
      const response = await uni.request({
        url: `${BASE_URL}/api/v1/auth/sms`,
        method: 'POST',
        data: { phone }
      })
      
      if (response.data.code === 200) {
        return { success: true }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      return { success: false, message: '网络错误' }
    }
  }
  
  // 初始化时从本地存储恢复数据
  const initFromStorage = () => {
    const storedToken = uni.getStorageSync('token')
    const storedUserInfo = uni.getStorageSync('userInfo')
    
    if (storedToken) {
      token.value = storedToken
    }
    
    if (storedUserInfo) {
      userInfo.value = { ...userInfo.value, ...storedUserInfo }
    }
  }
  
  // 初始化
  initFromStorage()
  
  return {
    // 状态
    token,
    userInfo,
    
    // 计算属性
    isLoggedIn,
    
    // 动作
    setToken,
    setUserInfo,
    login,
    register,
    getUserInfo,
    logout,
    sendSmsCode,
    initFromStorage
  }
})
